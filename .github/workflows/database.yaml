name: Migrate and Seed Database

on:
  push:
    branches:
      - main
      - preview
      - dev


jobs:
  migrate-and-seed:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm install

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Run Prisma Migrate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: npx prisma migrate deploy

      - name: Seed Database
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: npx prisma db seed