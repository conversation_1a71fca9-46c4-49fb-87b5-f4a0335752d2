import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";

export const { auth, handlers, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email as string,
          },
        });

        if (!user) {
          throw new Error("No account found with this email address");
        }

        if (!user.password) {
          throw new Error("This account cannot use password login");
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password as string,
          user.password
        );

        if (!isPasswordValid) {
          throw new Error("Incorrect password. Please try again");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub || '';

        // Add role from token to session user
        if (token.role) {
          session.user.role = token.role as string;
        }
      }
      console.log('Auth callback - Session user:', session.user);
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;

        // If this is the first sign in, fetch the user's role
        if (!token.role) {
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            select: { role: true }
          });

          if (dbUser) {
            token.role = dbUser.role;
          }
        }
      }
      console.log('Auth callback - JWT token:', token);
      return token;
    },
  },
  // Add trusted hosts configuration
  trustHost: true,
  // Debug mode for development
  debug: process.env.NODE_ENV === 'development',
});
