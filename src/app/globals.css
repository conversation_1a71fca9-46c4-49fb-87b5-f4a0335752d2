@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Font size adjustment based on user preference */
  :root {
    --font-size-adjustment: 1;
  }

  html {
    font-size: calc(16px * var(--font-size-adjustment));
  }

  /* Compact view styles */
  html.compact-view .card {
    @apply py-2;
  }

  html.compact-view .container {
    @apply py-2;
  }

  /* Fix for dark mode text contrast */
  .dark section {
    @apply bg-background text-foreground;
  }

  .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
    @apply text-foreground;
  }

  .dark p {
    @apply text-foreground;
  }

  /* Fix for home page sections */
  .dark .min-h-screen.bg-white {
    @apply bg-background;
  }

  .dark .border-gray-200 {
    @apply border-border;
  }

  .dark .text-gray-600 {
    @apply text-muted-foreground;
  }

  .dark .text-gray-500 {
    @apply text-muted-foreground;
  }

  .dark .bg-gray-50 {
    @apply bg-muted;
  }

  .dark .bg-gray-100 {
    @apply bg-muted;
  }

  .dark .text-gray-800 {
    @apply text-foreground;
  }

  .dark .bg-white {
    @apply bg-background;
  }

  /* Fix for buttons in dark mode */
  .dark .bg-black {
    @apply bg-primary;
  }

  .dark .hover\:bg-gray-800:hover {
    @apply hover:bg-primary/80;
  }

  .dark .bg-green-600 {
    @apply bg-primary;
  }

  .dark .hover\:bg-green-700:hover {
    @apply hover:bg-primary/80;
  }

  /* Fix for technology badge */
  .dark .bg-gray-100.text-gray-600 {
    @apply bg-muted text-muted-foreground;
  }

  /* Fix for category badges */
  .dark .bg-gray-100 {
    @apply bg-gray-800;
  }

  .dark .text-gray-800 {
    @apply text-gray-300;
  }

  /* Fix for No image placeholder */
  .dark .bg-gray-100 .text-gray-400 {
    @apply text-gray-500;
  }

  /* Fix for raw HTML tags showing in content */
  p:has(> p), h1:has(> h1), h2:has(> h2), h3:has(> h3), h4:has(> h4), h5:has(> h5), h6:has(> h6) {
    @apply hidden;
  }
}

/* Enhanced typography styles for post content */
.embed-renderer h1,
.embed-renderer h2,
.embed-renderer h3,
.embed-renderer h4,
.embed-renderer h5,
.embed-renderer h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5em;
  margin-bottom: 0.75em;
}

.embed-renderer h1 {
  font-size: 2rem;
}

.embed-renderer h2 {
  font-size: 1.75rem;
}

.embed-renderer h3 {
  font-size: 1.5rem;
}

.embed-renderer h4 {
  font-size: 1.25rem;
}

.embed-renderer p {
  margin-bottom: 1.25em;
  line-height: 1.7;
}

.embed-renderer ul,
.embed-renderer ol {
  margin-left: 1.5em;
  margin-bottom: 1.25em;
}

.embed-renderer li {
  margin-bottom: 0.5em;
}

.embed-renderer blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  margin: 1.5em 0;
}

.embed-renderer pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1.5em 0;
}

.dark .embed-renderer pre {
  background-color: #1f2937;
}

.embed-renderer code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.875em;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
}

.embed-renderer img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1.5em 0;
}

.embed-renderer a {
  color: #2563eb;
  text-decoration: none;
}

.embed-renderer a:hover {
  text-decoration: underline;
}

.dark .embed-renderer a {
  color: #60a5fa;
}

.embed-renderer table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
}

.embed-renderer th,
.embed-renderer td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
}

.dark .embed-renderer th,
.dark .embed-renderer td {
  border-color: #374151;
}

.embed-renderer th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .embed-renderer th {
  background-color: #1f2937;
}

/* Post content specific styles */
.post-content-wrapper {
  font-size: 1.125rem;
  line-height: 1.75;
}

.post-content-wrapper .embed-renderer {
  color: var(--foreground);
}

/* Ensure lists are properly styled */
.embed-renderer ul {
  list-style-type: disc;
}

.embed-renderer ol {
  list-style-type: decimal;
}

/* Ensure proper spacing for list items */
.embed-renderer li > ul,
.embed-renderer li > ol {
  margin-top: 0.5em;
  margin-bottom: 0;
}
