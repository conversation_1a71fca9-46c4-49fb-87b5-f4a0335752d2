/* Comment mention styles */
.comment-mention {
  background-color: rgba(var(--primary), 0.2);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  color: hsl(var(--primary));
  font-weight: 500;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  text-decoration: none;
  border: 1px solid rgba(var(--primary), 0.2);
  transition: all 0.15s ease;
}

.comment-mention:hover {
  background-color: rgba(var(--primary), 0.15);
  border-color: rgba(var(--primary), 0.3);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comment-mention {
    background-color: rgba(var(--primary), 0.15);
    border-color: rgba(var(--primary), 0.3);
  }

  .comment-mention:hover {
    background-color: rgba(var(--primary), 0.2);
    border-color: rgba(var(--primary), 0.4);
  }
}
