/**
 * Creates a mock Prisma client for testing
 * @returns A mock Prisma client
 */
export function createMockPrismaClient() {
  return {
    user: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    post: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    category: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    comment: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    like: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    follow: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    postCategory: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      createMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
      upsert: jest.fn(),
    },
    $transaction: jest.fn((callback) => {
      // Create a mock transaction client
      const txClient = {
        user: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        post: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        category: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        comment: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        like: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        follow: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
        postCategory: {
          findUnique: jest.fn(),
          findFirst: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          createMany: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          deleteMany: jest.fn(),
          count: jest.fn(),
          upsert: jest.fn(),
        },
      };
      
      return callback(txClient);
    }),
  };
}

/**
 * Mock data for testing
 */
export const mockData = {
  users: [
    {
      id: 'user-1',
      name: 'Test User 1',
      email: '<EMAIL>',
      image: null,
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'user-2',
      name: 'Test User 2',
      email: '<EMAIL>',
      image: null,
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'admin-1',
      name: 'Admin User',
      email: '<EMAIL>',
      image: null,
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  posts: [
    {
      id: 'post-1',
      title: 'Test Post 1',
      content: 'Test content 1',
      published: true,
      authorId: 'user-1',
      createdAt: new Date(),
      updatedAt: new Date(),
      viewCount: 10,
      likeCount: 5,
      commentCount: 2,
    },
    {
      id: 'post-2',
      title: 'Test Post 2',
      content: 'Test content 2',
      published: true,
      authorId: 'user-2',
      createdAt: new Date(),
      updatedAt: new Date(),
      viewCount: 5,
      likeCount: 2,
      commentCount: 1,
    },
  ],
  categories: [
    {
      id: 'category-1',
      name: 'Test Category 1',
      slug: 'test-category-1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'category-2',
      name: 'Test Category 2',
      slug: 'test-category-2',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
};
