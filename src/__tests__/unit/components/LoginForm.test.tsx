import { render, screen, waitFor } from '../../utils/test-utils';
import LoginForm from '@/app/components/LoginForm';
import { signIn, getProviders } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import type { UserEvent } from '@testing-library/user-event'
import '@testing-library/jest-dom';

// Using the mocks from jest.setup.js

describe('LoginForm', () => {
  // Mock setup
  const mockSignIn = signIn as jest.Mock;
  const mockGetProviders = getProviders as jest.Mock;
  const mockRouter = useRouter();

  // Test data
  const validEmail = '<EMAIL>';
  const validPassword = 'password123';
  const invalidEmail = 'invalid-email';

  // Helper function to fill and submit the form
  const fillAndSubmitForm = async (
    user: UserEvent,
    email = validEmail,
    password = validPassword
  ) => {
    await user.type(screen.getByLabelText(/email/i), email);
    await user.type(screen.getByLabelText(/password/i), password);

    // Get the submit button directly
    const submitButton = screen.getByRole('button', { name: /sign in$/i });
    await user.click(submitButton);
  };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup providers mock - resolve immediately to avoid act() warnings
    const mockProviders = {
      credentials: {
        id: 'credentials',
        name: 'Credentials',
        type: 'credentials',
      },
      google: {
        id: 'google',
        name: 'Google',
        type: 'oauth',
      },
    };

    mockGetProviders.mockImplementation(() => Promise.resolve(mockProviders));
  });

  it('renders the login form with all required elements', async () => {
    render(<LoginForm />);

    // Wait for the component to finish loading providers
    await waitFor(() => {
      // Check if form elements are rendered with correct attributes
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in$/i });

      // Verify elements are in the document
      expect(emailInput).toBeInTheDocument();
      expect(passwordInput).toBeInTheDocument();
      expect(submitButton).toBeInTheDocument();

      // Verify input types for accessibility
      expect(emailInput).toHaveAttribute('placeholder', 'Enter your email');
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(passwordInput).toHaveAttribute('placeholder', 'Enter your password');

      // Verify button is enabled
      expect(submitButton).toBeEnabled();
    });
  });

  // Accessibility tests removed as we simplified the test utils

  it('validates form inputs before submission', async () => {
    const { user } = render(<LoginForm />);

    // Try to submit without filling the form
    const submitButton = document.querySelector('button[type="submit"]');
    if (!submitButton) {
      throw new Error('Submit button not found');
    }
    await user.click(submitButton);

    // Check for validation messages
    const errorMessages = await screen.findAllByText(/required/i);
    expect(errorMessages).toHaveLength(2);

    // Fill in invalid email
    await user.clear(screen.getByLabelText(/email/i));
    await user.type(screen.getByLabelText(/email/i), invalidEmail);

    // Check for email validation message
    const emailError = await screen.findByText(/valid email/i);
    expect(emailError).toBeInTheDocument();

    // Verify form doesn't submit with invalid data
    expect(mockSignIn).not.toHaveBeenCalled();
  });

  it('shows error message when login fails', async () => {
    // Mock failed login
    mockSignIn.mockResolvedValue({ error: 'Invalid credentials' });

    const { user } = render(<LoginForm />);

    // Fill and submit form
    await fillAndSubmitForm(user);

    // Check if error message is displayed with correct styling
    const errorMessage = await screen.findByText(/login failed/i);
    expect(errorMessage).toBeInTheDocument();

    // Verify error has the correct styling
    const errorContainer = errorMessage.closest('div');
    expect(errorContainer).toHaveClass('bg-destructive/10');
    expect(errorContainer).toHaveClass('text-destructive');

    // Verify error icon is present (using querySelector instead of role)
    const alertIcon = errorContainer?.querySelector('svg');
    expect(alertIcon).toBeInTheDocument();
  });

  it('shows loading state during form submission', async () => {
    // Create a promise that we can resolve manually to control timing
    let resolveSignIn!: (value: { ok: boolean; error?: string | null }) => void;
    const signInPromise = new Promise<{ ok: boolean; error?: string | null }>(resolve => {
      resolveSignIn = resolve;
    });

    mockSignIn.mockImplementation(() => signInPromise);

    const { user } = render(<LoginForm />);

    // Fill and submit form
    await fillAndSubmitForm(user);

    // Resolve the promise to complete the sign-in process
    resolveSignIn({ ok: true });

    // Wait for the redirect to happen, which indicates the loading state is complete
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('redirects to dashboard on successful login', async () => {
    // Mock successful login
    mockSignIn.mockResolvedValue({ ok: true, error: null });

    const { user } = render(<LoginForm />);

    // Fill and submit form
    await fillAndSubmitForm(user);

    // Check if redirect happens
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
      expect(mockRouter.refresh).toHaveBeenCalled();
    });
  });

  it('renders OAuth provider buttons when available', async () => {
    // Mock providers with additional properties
    mockGetProviders.mockResolvedValue({
      credentials: {
        id: 'credentials',
        name: 'Credentials',
        type: 'credentials',
      },
      google: {
        id: 'google',
        name: 'Google',
        type: 'oauth',
        signinUrl: 'https://example.com/signin/google',
        callbackUrl: 'https://example.com/callback',
      },
    });

    render(<LoginForm />);

    // Wait for providers to load
    await waitFor(async () => {
      const googleButton = await screen.findByRole('button', { name: /sign in with google/i });
      expect(googleButton).toBeInTheDocument();

      // Verify button styling
      expect(googleButton).toHaveClass('w-full');
      // Check for border class instead of variant attribute
      expect(googleButton).toHaveClass('border');
    });
  });

  it('handles unexpected errors gracefully', async () => {
    // Mock signIn to throw an error
    mockSignIn.mockRejectedValue(new Error('Network error'));

    const { user } = render(<LoginForm />);

    // Fill and submit form
    await fillAndSubmitForm(user);

    // Check for generic error message
    const errorMessage = await screen.findByText(/login failed/i);
    expect(errorMessage).toBeInTheDocument();
  });
});
